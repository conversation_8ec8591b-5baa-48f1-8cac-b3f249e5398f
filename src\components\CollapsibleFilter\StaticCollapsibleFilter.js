import React, { useState, useRef } from "react";
import { Formik, Form, Field } from "formik";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Collapse,
  Box,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import CustomDropDown from "../../components/Dropdown/ReportsDropdownList";
import InputLabel from "../FormsUI/InputLabel/InputLabel";
import Button from "../../components/Button/Button";
import OutlinedButton from "../../components/Button/OutlinedButton";
import TextField from "../FormsUI/TextField";
import * as Yup from "yup";

export default function StaticCollapsibleFilter({
  setFilters,
  openFilterDialog,
  closeFilterDialog,
  filterData,
  setLabelData,
}) {
  const formikRef = useRef(null);
  const [expandedSections, setExpandedSections] = useState({
    customer: true,
    supplier: true,
    destination: true,
    interface: false,
    billing: false,
    traffic: false,
    other: false,
  });

  // Static data for dropdowns (no API calls)
  const staticData = {
    customers: [
      { value: "customer1", label: "Customer 1" },
      { value: "customer2", label: "Customer 2" },
      { value: "customer3", label: "Customer 3" },
    ],
    customerBind: [
      { value: "bind1", label: "Bind 1" },
      { value: "bind2", label: "Bind 2" },
    ],
    suppliers: [
      { value: "supplier1", label: "Supplier 1" },
      { value: "supplier2", label: "Supplier 2" },
      { value: "supplier3", label: "Supplier 3" },
    ],
    supplierBind: [
      { value: "sbind1", label: "Supplier Bind 1" },
      { value: "sbind2", label: "Supplier Bind 2" },
    ],
    destinationNameList: [
      { value: "dest1", label: "Destination 1" },
      { value: "dest2", label: "Destination 2" },
    ],
    destinationCountryList: [
      { value: "country1", label: "Country 1" },
      { value: "country2", label: "Country 2" },
    ],
    sourcePrime: [
      { value: "prime1", label: "Prime 1" },
      { value: "prime2", label: "Prime 2" },
    ],
    destinationPrime: [
      { value: "dprime1", label: "Dest Prime 1" },
      { value: "dprime2", label: "Dest Prime 2" },
    ],
    customerInterfaceType: [
      { value: "interface1", label: "Interface 1" },
      { value: "interface2", label: "Interface 2" },
    ],
    supplierInterfaceType: [
      { value: "sinterface1", label: "Supplier Interface 1" },
      { value: "sinterface2", label: "Supplier Interface 2" },
    ],
    customerBillingLogic: [
      { value: "billing1", label: "Billing Logic 1" },
      { value: "billing2", label: "Billing Logic 2" },
    ],
    supplierBillingLogic: [
      { value: "sbilling1", label: "Supplier Billing 1" },
      { value: "sbilling2", label: "Supplier Billing 2" },
    ],
    customerProtocol: [
      { value: "protocol1", label: "Protocol 1" },
      { value: "protocol2", label: "Protocol 2" },
    ],
    supplierProtocol: [
      { value: "sprotocol1", label: "Supplier Protocol 1" },
      { value: "sprotocol2", label: "Supplier Protocol 2" },
    ],
    lcrDataList: [
      { value: "lcr1", label: "LCR 1" },
      { value: "lcr2", label: "LCR 2" },
    ],
    specLCRDataList: [
      { value: "speclcr1", label: "Spec LCR 1" },
      { value: "speclcr2", label: "Spec LCR 2" },
    ],
    cdrStatus: [
      { value: "active", label: "Active" },
      { value: "inactive", label: "Inactive" },
    ],
    customerKam: [
      { value: "kam1", label: "KAM 1" },
      { value: "kam2", label: "KAM 2" },
    ],
    supplierKam: [
      { value: "skam1", label: "Supplier KAM 1" },
      { value: "skam2", label: "Supplier KAM 2" },
    ],
  };

  const initialValues = {
    customer_name: [],
    customer_bind: [],
    src_prime: [],
    destination: [],
    dest_prime: [],
    supplier: [],
    supplier_bind: [],
    destination_operator_name: [],
    destination_country_name: [],
    customer_interface_type: [],
    supplier_interface_type: [],
    customer_billing_logic: [],
    supplier_billing_logic: [],
    traffic_type_customer: [],
    traffic_type_supplier: [],
    destination_mcc_final: filterData?.destination_mcc_final ?? "",
    destination_mnc_final: filterData?.destination_mnc_final ?? "",
    lcr_name: [],
    spec_lcr: [],
    status: [],
    customer_kam: [],
    supplier_kam: [],
    roamingDirectStatus: "",
    negative_report: filterData?.negative_report || false,
    bilateral: filterData?.bilateral || false,
  };

  const toggleSection = (section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleSubmit = (values) => {
    const filteredValues = Object.keys(values)
      .filter((key) => {
        const value = values[key];
        return (
          value !== null &&
          value !== undefined &&
          value !== "" &&
          value !== false &&
          (!Array.isArray(value) || value.length > 0)
        );
      })
      .reduce((obj, key) => {
        obj[key] = values[key];
        return obj;
      }, {});

    setFilters(filteredValues);
    setLabelData(Object.keys(filteredValues));
    closeFilterDialog();
  };

  const validation = Yup.object().shape({
    destination_mcc_final: Yup.number()
      .typeError("Destination MCC must be a number")
      .integer("Destination MCC must be an integer")
      .max(999, "DestinationMCC must be a 3-digit code")
      .notOneOf([0], "Destination MCC cannot be 000"),
    destination_mnc_final: Yup.number()
      .typeError("Destination MNC must be a number")
      .integer("Destination MNC must be an integer")
      .max(999, "Destination MNC must be between 1 and 3 digits"),
  });

  const SectionHeader = ({ title, section, count = 0 }) => (
    <div
      className="flex items-center justify-between cursor-pointer p-3 bg-gray-50 hover:bg-gray-100 rounded-lg mb-2"
      onClick={() => toggleSection(section)}
    >
      <div className="flex items-center gap-2">
        <h3 className="text-sm font-medium text-gray-700">{title}</h3>
        {count > 0 && (
          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
            {count}
          </span>
        )}
      </div>
      {expandedSections[section] ? (
        <ExpandLessIcon className="text-gray-500" />
      ) : (
        <ExpandMoreIcon className="text-gray-500" />
      )}
    </div>
  );

  return (
    <div className="w-full mx-auto mt-5">
      <Dialog
        open={openFilterDialog}
        onClose={closeFilterDialog}
        fullWidth
        maxWidth="lg"
        aria-labelledby="filter-dialog-title"
        sx={{
          "& .MuiDialog-container": {
            "& .MuiPaper-root": {
              width: "100%",
              maxWidth: "1200px",
              minHeight: "400px",
              margin: 0,
            },
          },
        }}
      >
        <DialogTitle
          id="filter-dialog-title"
          sx={{ m: 0, p: 2, fontSize: "16px" }}
        >
          Filter Options
          <IconButton
            aria-label="close"
            onClick={closeFilterDialog}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon className="w-5 h-5" />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers sx={{ maxHeight: "70vh", overflow: "auto" }}>
          <Formik
            initialValues={initialValues}
            onSubmit={handleSubmit}
            innerRef={formikRef}
            enableReinitialize={true}
            validationSchema={validation}
          >
            {({ values, setFieldValue }) => (
              <Form>
                <div className="space-y-4">
                  {/* Customer Section */}
                  <div>
                    <SectionHeader
                      title="Customer Filters"
                      section="customer"
                      count={
                        (values.customer_name?.length || 0) +
                        (values.customer_bind?.length || 0) +
                        (values.src_prime?.length || 0) +
                        (values.customer_kam?.length || 0)
                      }
                    />
                    <Collapse in={expandedSections.customer}>
                      <Box className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-white border rounded-lg">
                        <div>
                          <InputLabel label="Customer Name" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.customers}
                            btnName="Select Customer Name"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue("customer_name", selectedDetail);
                            }}
                            value={values.customer_name}
                            defaultSelectedData={
                              filterData?.customer_name || []
                            }
                          />
                        </div>
                        <div>
                          <InputLabel label="Customer Bind" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.customerBind}
                            btnName="Select Customer Bind"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue("customer_bind", selectedDetail);
                            }}
                            value={values.customer_bind}
                            defaultSelectedData={
                              filterData?.customer_bind || []
                            }
                          />
                        </div>
                        <div>
                          <InputLabel label="Source Prime" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.sourcePrime}
                            btnName="Select Source Prime"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue("src_prime", selectedDetail);
                            }}
                            value={values.src_prime}
                            defaultSelectedData={filterData?.src_prime || []}
                          />
                        </div>
                        <div>
                          <InputLabel label="Customer KAM" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.customerKam}
                            btnName="Select Customer KAM"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue("customer_kam", selectedDetail);
                            }}
                            value={values.customer_kam}
                            defaultSelectedData={filterData?.customer_kam || []}
                          />
                        </div>
                      </Box>
                    </Collapse>
                  </div>

                  {/* Supplier Section */}
                  <div>
                    <SectionHeader
                      title="Supplier Filters"
                      section="supplier"
                      count={
                        (values.supplier?.length || 0) +
                        (values.supplier_bind?.length || 0) +
                        (values.supplier_kam?.length || 0)
                      }
                    />
                    <Collapse in={expandedSections.supplier}>
                      <Box className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-white border rounded-lg">
                        <div>
                          <InputLabel label="Supplier Name" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.suppliers}
                            btnName="Select Supplier Name"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue("supplier", selectedDetail);
                            }}
                            value={values.supplier}
                            defaultSelectedData={filterData?.supplier || []}
                          />
                        </div>
                        <div>
                          <InputLabel label="Supplier Bind" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.supplierBind}
                            btnName="Select Supplier Bind"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue("supplier_bind", selectedDetail);
                            }}
                            value={values.supplier_bind}
                            defaultSelectedData={
                              filterData?.supplier_bind || []
                            }
                          />
                        </div>
                        <div>
                          <InputLabel label="Supplier KAM" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.supplierKam}
                            btnName="Select Supplier KAM"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue("supplier_kam", selectedDetail);
                            }}
                            value={values.supplier_kam}
                            defaultSelectedData={filterData?.supplier_kam || []}
                          />
                        </div>
                      </Box>
                    </Collapse>
                  </div>

                  {/* Destination Section */}
                  <div>
                    <SectionHeader
                      title="Destination Filters"
                      section="destination"
                      count={
                        (values.destination?.length || 0) +
                        (values.destination_operator_name?.length || 0) +
                        (values.destination_country_name?.length || 0) +
                        (values.dest_prime?.length || 0) +
                        (values.destination_mcc_final ? 1 : 0) +
                        (values.destination_mnc_final ? 1 : 0)
                      }
                    />
                    <Collapse in={expandedSections.destination}>
                      <Box className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-white border rounded-lg">
                        <div>
                          <InputLabel label="Destination" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.destinationNameList}
                            btnName="Select Destination"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue("destination", selectedDetail);
                            }}
                            value={values.destination}
                            defaultSelectedData={filterData?.destination || []}
                          />
                        </div>
                        <div>
                          <InputLabel label="Destination Operator" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.destinationNameList}
                            btnName="Select Destination Operator"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue(
                                "destination_operator_name",
                                selectedDetail
                              );
                            }}
                            value={values.destination_operator_name}
                            defaultSelectedData={
                              filterData?.destination_operator_name || []
                            }
                          />
                        </div>
                        <div>
                          <InputLabel label="Destination Country" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.destinationCountryList}
                            btnName="Select Destination Country"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue(
                                "destination_country_name",
                                selectedDetail
                              );
                            }}
                            value={values.destination_country_name}
                            defaultSelectedData={
                              filterData?.destination_country_name || []
                            }
                          />
                        </div>
                        <div>
                          <InputLabel label="Destination Prime" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.destinationPrime}
                            btnName="Select Destination Prime"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue("dest_prime", selectedDetail);
                            }}
                            value={values.dest_prime}
                            defaultSelectedData={filterData?.dest_prime || []}
                          />
                        </div>
                        <div>
                          <InputLabel label="Destination MCC" />
                          <TextField
                            name="destination_mcc_final"
                            placeholder="Enter destination MCC"
                          />
                        </div>
                        <div>
                          <InputLabel label="Destination MNC" />
                          <TextField
                            name="destination_mnc_final"
                            placeholder="Enter destination MNC"
                          />
                        </div>
                      </Box>
                    </Collapse>
                  </div>

                  {/* Interface Section */}
                  <div>
                    <SectionHeader
                      title="Interface Filters"
                      section="interface"
                      count={
                        (values.customer_interface_type?.length || 0) +
                        (values.supplier_interface_type?.length || 0)
                      }
                    />
                    <Collapse in={expandedSections.interface}>
                      <Box className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-white border rounded-lg">
                        <div>
                          <InputLabel label="Customer Interface" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.customerInterfaceType}
                            btnName="Select Customer Interface"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue(
                                "customer_interface_type",
                                selectedDetail
                              );
                            }}
                            value={values.customer_interface_type}
                            defaultSelectedData={
                              filterData?.customer_interface_type || []
                            }
                          />
                        </div>
                        <div>
                          <InputLabel label="Supplier Interface" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.supplierInterfaceType}
                            btnName="Select Supplier Interface"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue(
                                "supplier_interface_type",
                                selectedDetail
                              );
                            }}
                            value={values.supplier_interface_type}
                            defaultSelectedData={
                              filterData?.supplier_interface_type || []
                            }
                          />
                        </div>
                      </Box>
                    </Collapse>
                  </div>

                  {/* Billing Section */}
                  <div>
                    <SectionHeader
                      title="Billing Logic Filters"
                      section="billing"
                      count={
                        (values.customer_billing_logic?.length || 0) +
                        (values.supplier_billing_logic?.length || 0)
                      }
                    />
                    <Collapse in={expandedSections.billing}>
                      <Box className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-white border rounded-lg">
                        <div>
                          <InputLabel label="Customer Billing Logic" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.customerBillingLogic}
                            btnName="Select Customer Billing Logic"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue(
                                "customer_billing_logic",
                                selectedDetail
                              );
                            }}
                            value={values.customer_billing_logic}
                            defaultSelectedData={
                              filterData?.customer_billing_logic || []
                            }
                          />
                        </div>
                        <div>
                          <InputLabel label="Supplier Billing Logic" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.supplierBillingLogic}
                            btnName="Select Supplier Billing Logic"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue(
                                "supplier_billing_logic",
                                selectedDetail
                              );
                            }}
                            value={values.supplier_billing_logic}
                            defaultSelectedData={
                              filterData?.supplier_billing_logic || []
                            }
                          />
                        </div>
                      </Box>
                    </Collapse>
                  </div>

                  {/* Traffic Section */}
                  <div>
                    <SectionHeader
                      title="Traffic Type Filters"
                      section="traffic"
                      count={
                        (values.traffic_type_customer?.length || 0) +
                        (values.traffic_type_supplier?.length || 0)
                      }
                    />
                    <Collapse in={expandedSections.traffic}>
                      <Box className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-white border rounded-lg">
                        <div>
                          <InputLabel label="Customer Traffic Type" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.customerProtocol}
                            btnName="Select Customer Traffic Type"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue(
                                "traffic_type_customer",
                                selectedDetail
                              );
                            }}
                            value={values.traffic_type_customer}
                            defaultSelectedData={
                              filterData?.traffic_type_customer || []
                            }
                          />
                        </div>
                        <div>
                          <InputLabel label="Supplier Traffic Type" />
                          <CustomDropDown
                            btnWidth="w-full"
                            data={staticData.supplierProtocol}
                            btnName="Select Supplier Traffic Type"
                            onSelectionChange={(selectedDetail) => {
                              setFieldValue(
                                "traffic_type_supplier",
                                selectedDetail
                              );
                            }}
                            value={values.traffic_type_supplier}
                            defaultSelectedData={
                              filterData?.traffic_type_supplier || []
                            }
                          />
                        </div>
                      </Box>
                    </Collapse>
                  </div>

                  {/* Other Filters Section */}
                  <div>
                    <SectionHeader
                      title="Other Filters"
                      section="other"
                      count={
                        (values.lcr_name?.length || 0) +
                        (values.spec_lcr?.length || 0) +
                        (values.status?.length || 0) +
                        (values.roamingDirectStatus ? 1 : 0) +
                        (values.negative_report ? 1 : 0) +
                        (values.bilateral ? 1 : 0)
                      }
                    />
                    <Collapse in={expandedSections.other}>
                      <Box className="space-y-4 p-4 bg-white border rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <InputLabel label="LCR Name" />
                            <CustomDropDown
                              btnWidth="w-full"
                              data={staticData.lcrDataList}
                              btnName="Select LCR Name"
                              onSelectionChange={(selectedDetail) => {
                                setFieldValue("lcr_name", selectedDetail);
                              }}
                              value={values.lcr_name}
                              defaultSelectedData={filterData?.lcr_name || []}
                            />
                          </div>
                          <div>
                            <InputLabel label="Spec LCR" />
                            <CustomDropDown
                              btnWidth="w-full"
                              data={staticData.specLCRDataList}
                              btnName="Select Spec LCR"
                              onSelectionChange={(selectedDetail) => {
                                setFieldValue("spec_lcr", selectedDetail);
                              }}
                              value={values.spec_lcr}
                              defaultSelectedData={filterData?.spec_lcr || []}
                            />
                          </div>
                          <div>
                            <InputLabel label="Status" />
                            <CustomDropDown
                              btnWidth="w-full"
                              data={staticData.cdrStatus}
                              btnName="Select Status"
                              onSelectionChange={(selectedDetail) => {
                                setFieldValue("status", selectedDetail);
                              }}
                              value={values.status}
                              defaultSelectedData={filterData?.status || []}
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-3">
                              Roaming and Direct Status:
                            </label>
                            <div className="flex flex-col gap-2">
                              <label className="inline-flex items-center">
                                <Field
                                  type="radio"
                                  name="roamingDirectStatus"
                                  value="only_roaming"
                                  className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">
                                  Only Roaming
                                </span>
                              </label>
                              <label className="inline-flex items-center">
                                <Field
                                  type="radio"
                                  name="roamingDirectStatus"
                                  value="only_direct"
                                  className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">
                                  Only Direct
                                </span>
                              </label>
                              <label className="inline-flex items-center">
                                <Field
                                  type="radio"
                                  name="roamingDirectStatus"
                                  value="both"
                                  className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">
                                  Both
                                </span>
                              </label>
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-3">
                              Additional Options:
                            </label>
                            <div className="flex flex-col gap-2">
                              <label className="inline-flex items-center">
                                <Field
                                  type="checkbox"
                                  name="negative_report"
                                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">
                                  Negative Report Required
                                </span>
                              </label>
                              <label className="inline-flex items-center">
                                <Field
                                  type="checkbox"
                                  name="bilateral"
                                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">
                                  Include Bilateral
                                </span>
                              </label>
                            </div>
                          </div>
                        </div>
                      </Box>
                    </Collapse>
                  </div>
                </div>
              </Form>
            )}
          </Formik>
        </DialogContent>

        <DialogActions>
          <div className="flex gap-4">
            <Button
              label="Clear"
              type="button"
              buttonClassName="w-24"
              onClick={() => {
                if (formikRef.current) {
                  formikRef.current.resetForm();
                  setFilters({});
                }
              }}
            />
            <OutlinedButton
              label="Apply"
              type="button"
              buttonClassName="w-24"
              onClick={() => {
                if (formikRef.current) {
                  formikRef.current.handleSubmit();
                }
              }}
            />
          </div>
        </DialogActions>
      </Dialog>
    </div>
  );
}
