import React, { useState } from "react";
import CDRStyleCollapsibleFilter from "./CDRStyleCollapsibleFilter";
import Button from "../Button/Button";

export default function StaticCollapsibleFilterDemo() {
  const [filters, setFilters] = useState({});
  const [labelData, setLabelData] = useState([]);

  const handleClearFilters = () => {
    setFilters({});
    setLabelData([]);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">
          Static Collapsible Filter Demo
        </h1>

        <div className="flex gap-4 mb-6">
          <Button
            label="Clear All Filters"
            onClick={handleClearFilters}
            buttonClassName="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
          />
        </div>

        {/* Display Applied Filters */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-3">
            Applied Filters ({Object.keys(filters).length})
          </h3>
          {Object.keys(filters).length > 0 ? (
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(filters).map(([key, value]) => (
                  <div key={key} className="bg-white p-3 rounded border">
                    <div className="font-medium text-gray-700 capitalize">
                      {key.replace(/_/g, " ")}:
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {Array.isArray(value)
                        ? value
                            .map((item) => item.label || item.value || item)
                            .join(", ")
                        : typeof value === "boolean"
                        ? value
                          ? "Yes"
                          : "No"
                        : value}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-gray-500 italic">No filters applied</div>
          )}
        </div>

        {/* Display Filter Labels */}
        {labelData.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-700 mb-3">
              Filter Labels
            </h3>
            <div className="flex flex-wrap gap-2">
              {labelData.map((label, index) => (
                <span
                  key={index}
                  className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                >
                  {label}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Component Features */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">
            Component Features
          </h3>
          <ul className="list-disc list-inside text-blue-700 space-y-1">
            <li>Collapsible sections with expand/collapse functionality</li>
            <li>Static data (no API calls required)</li>
            <li>
              Multiple filter categories: Customer, Supplier, Destination,
              Interface, Billing, Traffic, and Other
            </li>
            <li>Real-time filter count display in section headers</li>
            <li>Form validation for MCC/MNC fields</li>
            <li>Responsive grid layout</li>
            <li>Clear and Apply functionality</li>
            <li>
              Support for dropdowns, text fields, radio buttons, and checkboxes
            </li>
          </ul>
        </div>
      </div>

      {/* The Filter Component */}
      <CDRStyleCollapsibleFilter
        setFilters={setFilters}
        filterData={filters}
        setLabelData={setLabelData}
      />
    </div>
  );
}
