# Static Collapsible Filter Component

A React component that provides a collapsible filter interface with multiple sections and static data (no API calls required).

## Features

- **Collapsible Sections**: Each filter category can be expanded or collapsed independently
- **Static Data**: Uses predefined static data instead of API calls
- **Multiple Filter Types**: Supports dropdowns, text fields, radio buttons, and checkboxes
- **Real-time Counts**: Shows the number of applied filters in each section header
- **Form Validation**: Includes validation for MCC/MNC fields
- **Responsive Design**: Adapts to different screen sizes
- **Filter Categories**:
  - Customer Filters (Customer Name, Bind, Source Prime, KAM)
  - Supplier Filters (Supplier Name, Bind, KAM)
  - Destination Filters (Destination, Operator, Country, Prime, MCC, MNC)
  - Interface Filters (Customer/Supplier Interface Types)
  - Billing Logic Filters (Customer/Supplier Billing Logic)
  - Traffic Type Filters (Customer/Supplier Traffic Types)
  - Other Filters (LCR, Spec LCR, Status, Roaming/Direct options, Additional options)

## Usage

```jsx
import StaticCollapsibleFilter from './StaticCollapsibleFilter';

function MyComponent() {
  const [openFilterDialog, setOpenFilterDialog] = useState(false);
  const [filters, setFilters] = useState({});
  const [labelData, setLabelData] = useState([]);

  return (
    <div>
      <button onClick={() => setOpenFilterDialog(true)}>
        Open Filter
      </button>
      
      <StaticCollapsibleFilter
        setFilters={setFilters}
        openFilterDialog={openFilterDialog}
        closeFilterDialog={() => setOpenFilterDialog(false)}
        filterData={filters}
        setLabelData={setLabelData}
      />
    </div>
  );
}
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `setFilters` | Function | Yes | Callback function to handle filter changes |
| `openFilterDialog` | Boolean | Yes | Controls whether the filter dialog is open |
| `closeFilterDialog` | Function | Yes | Function to close the filter dialog |
| `filterData` | Object | No | Previously applied filter data for initialization |
| `setLabelData` | Function | Yes | Callback function to handle filter label changes |

## Filter Data Structure

The component returns filter data in the following format:

```javascript
{
  customer_name: [{ value: "customer1", label: "Customer 1" }],
  customer_bind: [{ value: "bind1", label: "Bind 1" }],
  destination_mcc_final: "123",
  destination_mnc_final: "45",
  roamingDirectStatus: "only_roaming",
  negative_report: true,
  bilateral: false,
  // ... other filters
}
```

## Customization

### Modifying Static Data

To customize the dropdown options, edit the `staticData` object in the component:

```javascript
const staticData = {
  customers: [
    { value: "customer1", label: "Customer 1" },
    { value: "customer2", label: "Customer 2" },
    // Add more options...
  ],
  // ... other data arrays
};
```

### Adding New Filter Sections

1. Add a new section to the `expandedSections` state
2. Create a new section in the JSX with `SectionHeader` and `Collapse` components
3. Add the corresponding filter fields inside the `Box` component

### Styling

The component uses Tailwind CSS classes. You can customize the appearance by modifying the className props throughout the component.

## Dependencies

- React
- Formik (for form handling)
- Material-UI (for Dialog, Collapse, and Icons)
- Yup (for validation)
- Custom components: CustomDropDown, InputLabel, Button, OutlinedButton, TextField

## Demo

See `StaticCollapsibleFilterDemo.js` for a complete working example of how to implement and use this component.
