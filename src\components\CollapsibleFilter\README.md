# CDR Style Collapsible Filter Component

A React component that provides a simple expand/collapse filter interface similar to CDRSearch.js, using the same metadata structure as ReportFilter.js.

## Features

- **Simple Expand/Collapse**: Single toggle to show/hide all filters (similar to CDRSearch.js)
- **Metadata Integration**: Uses the same metadata structure as ReportFilter.js
- **Multiple Filter Types**: Supports dropdowns, text fields, radio buttons, and checkboxes
- **Form Validation**: Includes validation for MCC/MNC fields
- **Responsive Grid Layout**: Adapts to different screen sizes with responsive grid
- **Comprehensive Filters**: All major filter types in a single view when expanded
  - Customer Filters (Name, Bind, Source Prime, KAM)
  - Supplier Filters (Name, Bind, KAM)
  - Destination Filters (Destination, Country, Prime, MCC, MNC)
  - Interface Filters (Customer/Supplier Interface Types)
  - Billing Logic Filters (Customer/Supplier Billing Logic)
  - Traffic Type Filters (Customer/Supplier Traffic Types)
  - Other Filters (LCR, Spec LCR, Status, Roaming/Direct options, Additional options)

## Usage

```jsx
import CDRStyleCollapsibleFilter from "./CDRStyleCollapsibleFilter";

function MyComponent() {
  const [filters, setFilters] = useState({});
  const [labelData, setLabelData] = useState([]);

  return (
    <div>
      <CDRStyleCollapsibleFilter
        setFilters={setFilters}
        filterData={filters}
        setLabelData={setLabelData}
      />
    </div>
  );
}
```

## Props

| Prop           | Type     | Required | Description                                       |
| -------------- | -------- | -------- | ------------------------------------------------- |
| `setFilters`   | Function | Yes      | Callback function to handle filter changes        |
| `filterData`   | Object   | No       | Previously applied filter data for initialization |
| `setLabelData` | Function | Yes      | Callback function to handle filter label changes  |

## Filter Data Structure

The component returns filter data in the following format:

```javascript
{
  customer_name: [{ value: "customer1", label: "Customer 1" }],
  customer_bind: [{ value: "bind1", label: "Bind 1" }],
  destination_mcc_final: "123",
  destination_mnc_final: "45",
  roamingDirectStatus: "only_roaming",
  negative_report: true,
  bilateral: false,
  // ... other filters
}
```

## Customization

### Modifying Static Data

To customize the dropdown options, edit the `staticData` object in the component:

```javascript
const staticData = {
  customers: [
    { value: "customer1", label: "Customer 1" },
    { value: "customer2", label: "Customer 2" },
    // Add more options...
  ],
  // ... other data arrays
};
```

### Adding New Filter Sections

1. Add a new section to the `expandedSections` state
2. Create a new section in the JSX with `SectionHeader` and `Collapse` components
3. Add the corresponding filter fields inside the `Box` component

### Styling

The component uses Tailwind CSS classes. You can customize the appearance by modifying the className props throughout the component.

## Dependencies

- React
- Formik (for form handling)
- Material-UI (for Dialog, Collapse, and Icons)
- Yup (for validation)
- Custom components: CustomDropDown, InputLabel, Button, OutlinedButton, TextField

## Demo

See `StaticCollapsibleFilterDemo.js` for a complete working example of how to implement and use this component.
